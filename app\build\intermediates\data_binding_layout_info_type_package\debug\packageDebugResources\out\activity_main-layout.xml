<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.yancao.qrscanner" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="148" endOffset="51"/></Target><Target id="@+id/preview_view" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="9" startOffset="4" endLine="17" endOffset="38"/></Target><Target id="@+id/qr_overlay_view" view="com.yancao.qrscanner.ui.QrCodeOverlayView"><Expressions/><location startLine="20" startOffset="4" endLine="27" endOffset="62"/></Target><Target id="@+id/btn_flashlight" view="Button"><Expressions/><location startLine="32" startOffset="4" endLine="44" endOffset="34"/></Target><Target id="@+id/zoom_control_panel" view="FrameLayout"><Expressions/><location startLine="47" startOffset="4" endLine="91" endOffset="17"/></Target><Target id="@+id/zoom_slider_container" view="LinearLayout"><Expressions/><location startLine="57" startOffset="8" endLine="89" endOffset="22"/></Target><Target id="@+id/tv_zoom_ratio" view="TextView"><Expressions/><location startLine="68" startOffset="12" endLine="77" endOffset="51"/></Target><Target id="@+id/seek_bar_zoom" view="SeekBar"><Expressions/><location startLine="80" startOffset="12" endLine="87" endOffset="58"/></Target><Target id="@+id/control_panel" view="LinearLayout"><Expressions/><location startLine="94" startOffset="4" endLine="137" endOffset="18"/></Target><Target id="@+id/button_container" view="LinearLayout"><Expressions/><location startLine="106" startOffset="8" endLine="135" endOffset="22"/></Target><Target id="@+id/btn_scan" view="Button"><Expressions/><location startLine="114" startOffset="12" endLine="122" endOffset="58"/></Target><Target id="@+id/btn_broadcast" view="Button"><Expressions/><location startLine="125" startOffset="12" endLine="133" endOffset="58"/></Target><Target id="@+id/tv_scan_results" view="TextView"><Expressions/><location startLine="138" startOffset="4" endLine="146" endOffset="43"/></Target></Targets></Layout>