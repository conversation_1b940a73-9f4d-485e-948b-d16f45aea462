<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/exposure_control_bg"
    android:orientation="vertical"
    android:padding="12dp">

    <!-- 曝光控制标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="8dp"
            android:src="@android:drawable/ic_menu_camera"
            app:tint="@color/text_primary_light" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="曝光控制"
            android:textColor="@color/text_primary_light"
            android:textSize="14sp"
            android:textStyle="bold" />

        <Switch
            android:id="@+id/switch_exposure_control"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:thumbTint="@color/button_accent"
            android:trackTint="@color/text_secondary_light" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="8dp"
        android:background="@color/text_secondary_light" />

    <!-- 曝光状态显示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="当前曝光:"
            android:textColor="@color/text_secondary_light"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_exposure_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0.0 EV"
            android:textColor="@color/text_primary_light"
            android:textSize="12sp"
            android:textStyle="bold" />
    </LinearLayout>

    <!-- 曝光控制面板 -->
    <LinearLayout
        android:id="@+id/exposure_control_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        android:visibility="gone">

        <!-- 快速调节按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_decrease_exposure"
                style="@style/ExposureControlButton"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:background="@color/button_primary"
                android:drawableStart="@android:drawable/ic_media_previous"
                android:drawableTint="@android:color/white"
                android:text="减少"
                android:textSize="10sp" />

            <Button
                android:id="@+id/btn_reset_exposure"
                style="@style/ExposureControlButton"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginHorizontal="4dp"
                android:layout_weight="1"
                android:background="@color/button_secondary"
                android:text="重置"
                android:textSize="10sp" />

            <Button
                android:id="@+id/btn_increase_exposure"
                style="@style/ExposureControlButton"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:background="@color/button_primary"
                android:drawableEnd="@android:drawable/ic_media_next"
                android:drawableTint="@android:color/white"
                android:text="增加"
                android:textSize="10sp" />
        </LinearLayout>

        <!-- 精确调节滑动条 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:gravity="center"
                android:text="精确调节"
                android:textColor="@color/text_secondary_light"
                android:textSize="10sp" />

            <SeekBar
                android:id="@+id/seek_bar_exposure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:progressTint="@color/button_accent"
                android:thumbTint="@color/button_accent" />
        </LinearLayout>

        <!-- 预设模式按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_optimize_for_scanning"
                style="@style/ExposureControlButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:background="@color/button_accent"
                android:text="扫码优化"
                android:textSize="9sp" />

            <Button
                android:id="@+id/btn_low_light_mode"
                style="@style/ExposureControlButton"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:background="@color/exposure_dark"
                android:text="低光模式"
                android:textSize="9sp" />
        </LinearLayout>
    </LinearLayout>

    <!-- 展开/收起按钮 -->
    <Button
        android:id="@+id/btn_toggle_exposure_panel"
        android:layout_width="match_parent"
        android:layout_height="28dp"
        android:layout_marginTop="8dp"
        android:background="@color/button_tertiary"
        android:text="显示详细控制"
        android:textColor="@android:color/white"
        android:textSize="9sp" />

</LinearLayout>